  nlweb:
    image:  oideibrett/nlweb:latest
    #container_name: nlweb
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - PYTHONPATH=/app
      - PORT=8000
      - NLWEB_CONFIG_DIR=/app/config
    volumes:
      - ./config/nlweb/data:/app/data
      - ./config/nlweb/nlweb_config:/app/config:ro
    healthcheck:
      test: ["CMD-SHELL", "python -c \"import urllib.request; urllib.request.urlopen('http://localhost:8000')\""]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    user: root

