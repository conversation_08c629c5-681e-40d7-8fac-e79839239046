  # Komodo Database Services
  postgres:
    image: ghcr.io/ferretdb/postgres-documentdb
    #container_name: komodo-postgres-1
    labels:
      komodo.skip: # Prevent Komodo from stopping with StopAllContainers
    restart: unless-stopped
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: postgres
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  ferretdb:
    image: ghcr.io/ferretdb/ferretdb
    #container_name: komodo-ferretdb-1
    labels:
      komodo.skip: # Prevent Komodo from stopping with StopAllContainers
    restart: unless-stopped
    depends_on:
      - postgres
    volumes:
      - ferretdb-state:/state
    environment:
      FERRETDB_POSTGRESQL_URL: postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/postgres

  # Komodo Core
  komodo-core:
    image: ghcr.io/moghtech/komodo-core:${COMPOSE_KOMODO_IMAGE_TAG:-latest}
    #container_name: komodo-core-1
    labels:
      komodo.skip: # Prevent Komodo from stopping with StopAllContainers
    restart: unless-stopped
    depends_on:
      - ferretdb
    ports:
      - 9120:9120
    environment:
      KOMODO_DATABASE_ADDRESS: ferretdb:27017
      KOMODO_DATABASE_USERNAME: ${POSTGRES_USER}
      KOMODO_DATABASE_PASSWORD: ${POSTGRES_PASSWORD}
      KOMODO_PASSKEY: ${KOMODO_PASSKEY}
      TZ: ${TZ:-Etc/UTC}
      KOMODO_HOST: https://komodo.${DOMAIN}
      KOMODO_TITLE: ${KOMODO_TITLE:-Komodo}
      KOMODO_FIRST_SERVER: ${KOMODO_FIRST_SERVER}
      KOMODO_DISABLE_CONFIRM_DIALOG: ${KOMODO_DISABLE_CONFIRM_DIALOG:-false}
      KOMODO_MONITORING_INTERVAL: ${KOMODO_MONITORING_INTERVAL:-15-sec}
      KOMODO_RESOURCE_POLL_INTERVAL: ${KOMODO_RESOURCE_POLL_INTERVAL:-1-hr}
      KOMODO_WEBHOOK_SECRET: ${KOMODO_WEBHOOK_SECRET}
      KOMODO_JWT_SECRET: ${KOMODO_JWT_SECRET}
      KOMODO_JWT_TTL: ${KOMODO_JWT_TTL:-1-day}
      KOMODO_LOCAL_AUTH: ${KOMODO_LOCAL_AUTH:-true}
      KOMODO_DISABLE_USER_REGISTRATION: ${KOMODO_DISABLE_USER_REGISTRATION:-false}
      KOMODO_ENABLE_NEW_USERS: ${KOMODO_ENABLE_NEW_USERS:-false}
      KOMODO_DISABLE_NON_ADMIN_CREATE: ${KOMODO_DISABLE_NON_ADMIN_CREATE:-false}
      KOMODO_TRANSPARENT_MODE: ${KOMODO_TRANSPARENT_MODE:-false}
      KOMODO_LOGGING_PRETTY: ${KOMODO_LOGGING_PRETTY:-false}
      KOMODO_PRETTY_STARTUP_CONFIG: ${KOMODO_PRETTY_STARTUP_CONFIG:-false}
      KOMODO_OIDC_ENABLED: ${KOMODO_OIDC_ENABLED:-false}
      KOMODO_GITHUB_OAUTH_ENABLED: ${KOMODO_GITHUB_OAUTH_ENABLED:-false}
      KOMODO_GOOGLE_OAUTH_ENABLED: ${KOMODO_GOOGLE_OAUTH_ENABLED:-false}
      KOMODO_AWS_ACCESS_KEY_ID: ${KOMODO_AWS_ACCESS_KEY_ID}
      KOMODO_AWS_SECRET_ACCESS_KEY: ${KOMODO_AWS_SECRET_ACCESS_KEY}
    volumes:
      - repo-cache:/repo-cache
