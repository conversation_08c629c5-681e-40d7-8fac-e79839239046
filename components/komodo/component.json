{"name": "komodo", "required_env": ["KOMODO_HOST_IP", "KOMODO_PASSKEY"], "optional_env": ["POSTGRES_USER", "POSTGRES_PASSWORD", "COMPOSE_KOMODO_IMAGE_TAG", "DOMAIN", "TZ", "KOMODO_TITLE", "KOMODO_FIRST_SERVER", "KOMODO_DISABLE_CONFIRM_DIALOG", "KOMODO_MONITORING_INTERVAL", "KOMODO_RESOURCE_POLL_INTERVAL", "KOMODO_WEBHOOK_SECRET", "KOMODO_JWT_SECRET", "KOMODO_JWT_TTL", "KOMODO_LOCAL_AUTH", "KOMODO_DISABLE_USER_REGISTRATION", "KOMODO_ENABLE_NEW_USERS", "KOMODO_DISABLE_NON_ADMIN_CREATE", "KOMODO_TRANSPARENT_MODE", "KOMODO_LOGGING_PRETTY", "KOMODO_PRETTY_STARTUP_CONFIG", "KOMODO_OIDC_ENABLED", "KOMODO_GITHUB_OAUTH_ENABLED", "KOMODO_GOOGLE_OAUTH_ENABLED", "KOMODO_AWS_ACCESS_KEY_ID", "KOMODO_AWS_SECRET_ACCESS_KEY", "KOMODO_SUBDOMAIN"]}