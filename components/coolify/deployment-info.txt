🚀 Coolify Platform Deployment

Deployment completed at: $(date)

📊 Configuration:
- Platform: Coolify
- Database: PostgreSQL
- Cache: Redis
- WebSocket: Soketi
- App Port: ${APP_PORT:-8000}

🚨 IMPORTANT SERVER PREREQUISITES:
Before running 'docker compose up -d', you MUST run these commands on your server:

1. Create Directories:
   mkdir -p /data/coolify/{source,ssh,applications,databases,backups,services,proxy,webhooks-during-maintenance}
   mkdir -p /data/coolify/ssh/{keys,mux}
   mkdir -p /data/coolify/proxy/dynamic

2. Generate & Add SSH Key:
   ssh-keygen -f /data/coolify/ssh/keys/<EMAIL> -t ed25519 -N '' -C root@coolify
   cat /data/coolify/ssh/keys/<EMAIL> >> ~/.ssh/authorized_keys
   chmod 600 ~/.ssh/authorized_keys

3. Set Permissions:
   chown -R 9999:root /data/coolify
   chmod -R 700 /data/coolify

4. Create Docker Network:
   docker network create --attachable coolify

5. Copy Custom Domain Configuration:
   cp config/coolify/custom_domain.yml /data/coolify/proxy/dynamic/

🌐 Access Information:
- Coolify Dashboard: https://${ADMIN_SUBDOMAIN:-coolify}.${DOMAIN}
- Database: PostgreSQL on port 5432
- Redis: Redis on port 6379
- WebSocket: Soketi on port ${SOKETI_PORT:-6001}

📁 Directory Structure Created:
./data/coolify/
├── source/
├── ssh/
│   ├── keys/
│   └── mux/
├── applications/         # Application data
├── databases/           # Database backups
├── services/            # Service configurations
├── backups/             # System backups
├── proxy/
│   └── dynamic/
└── webhooks-during-maintenance/

🔧 Next Steps:
1. Complete the server prerequisites above
2. Run: docker compose up -d
3. Access Coolify at https://${ADMIN_SUBDOMAIN:-coolify}.${DOMAIN}
4. Complete the initial setup wizard
5. Configure your first application deployment

📚 Documentation:
- Coolify Docs: https://coolify.io/docs
- GitHub: https://github.com/coollabsio/coolify
- Manual Setup Guide: https://coolify.io/docs/installation#manual

