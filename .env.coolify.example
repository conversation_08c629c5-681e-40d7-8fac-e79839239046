# Coolify Platform Environment Variables
# Copy this file to .env and fill in the values
# If values are not provided, they will be auto-generated during setup

# Required Coolify Configuration
# These will be auto-generated if not provided
APP_ID=
APP_KEY=
DB_USERNAME=coolify
DB_PASSWORD=
REDIS_PASSWORD=
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=

# Optional Configuration
DB_DATABASE=coolify
APP_ENV=production
APP_NAME=Coolify
APP_PORT=8000
SOKETI_PORT=6001
SOKETI_DEBUG=false

# Registry Configuration (optional)
REGISTRY_URL=ghcr.io
LATEST_IMAGE=latest

# PHP Configuration (optional)
PHP_MEMORY_LIMIT=256M
PHP_FPM_PM_CONTROL=dynamic
PHP_FPM_PM_START_SERVERS=1
PHP_FPM_PM_MIN_SPARE_SERVERS=1
PHP_FPM_PM_MAX_SPARE_SERVERS=10

# Auto-generate missing values using OpenSSL:
# sed -i "s|APP_ID=.*|APP_ID=$(openssl rand -hex 16)|g" .env
# sed -i "s|APP_KEY=.*|APP_KEY=base64:$(openssl rand -base64 32)|g" .env
# sed -i "s|DB_PASSWORD=.*|DB_PASSWORD=$(openssl rand -base64 32)|g" .env
# sed -i "s|REDIS_PASSWORD=.*|REDIS_PASSWORD=$(openssl rand -base64 32)|g" .env
# sed -i "s|PUSHER_APP_ID=.*|PUSHER_APP_ID=$(openssl rand -hex 32)|g" .env
# sed -i "s|PUSHER_APP_KEY=.*|PUSHER_APP_KEY=$(openssl rand -hex 32)|g" .env
# sed -i "s|PUSHER_APP_SECRET=.*|PUSHER_APP_SECRET=$(openssl rand -hex 32)|g" .env
