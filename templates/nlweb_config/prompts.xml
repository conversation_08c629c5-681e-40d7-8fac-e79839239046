<?xml version="1.0" encoding="UTF-8"?>
<root xmlns="http://nlweb.ai/base"
      xmlns:so="http://www.schema.org/"
      xmlns:rdfs="http://www.w3.org/rdfs">

<!-- This file has all the prompts that are used in the process of running a query. -->

  <Item>

    <Prompt ref="DetectIrrelevantQueryPrompt">
      <promptString>
        The user is querying the site {request.site} which has information about {site.itemType}s.
        Is the site utterly completely irrelevant to the user's query? 
        The question is not whether this is the best site for answering the query, 
        but if there is nothing on the site that is likely to be relevant for the query. 
        If the site is irrelevant, add an explanation for why it is irrelevant. Otherwise, leave that field blank.
        The user query is: '{request.query}'
      </promptString>
      <returnStruc>
        {
          "site_is_irrelevant_to_query": "True or False",
          "explanation_for_irrelevance": "Explanation for why the site is irrelevant"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="PrevQueryDecontextualizer">
      <promptString>
        The user is querying the site {request.site} which has {site.itemType}s.
        Rewrite the query, incorporating the context of the previous queries and answers.
        Keep the decontextualized query short and do not reference the site. 

        If the query very clearly does not reference earlier queries, 
        don't change the query. Err on the side of incorporating the context of the 
        previous queries. If you are not sure whether this is a brand new query, 
        or follow up, it is likely a follow up. Try your best to incorporate the 
        context from the previous queries.

        The user's query is: {request.rawQuery}. 
        Previous queries were: {request.previousQueries}.
       <!-- Previous answers (title + url)provided were: {request.prevAnswers}. -->
      </promptString>
      <returnStruc>
        {
          "requires_decontextualization": "True or False",
          "decontextualized_query": "The rewritten query, if decontextualization is required"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="DecontextualizeContextPrompt">
      <promptString>
        The user is asking the following question: '{request.rawQuery}' in the context of 
        the an item with the following description: {request.contextDescription}. 
        Previous answers provided were: {request.prevAnswers}.
        Rewrite the query to decontextualize it so that it can be answered 
        without reference to earlier queries, previous answers, or to the item description.
      </promptString>
      <returnStruc>
        {
          "decontextualized_query": "The rewritten query"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="FullDecontextualizePrompt">
      <promptString>
        The user is asking the following question: '{request.rawQuery}' in the context of 
        the an item with the following description: {request.contextDescription}. 
        Previous queries from the user were: {request.previousQueries}.
        Previous answers provided were: {request.prevAnswers}.
        Rewrite the query to decontextualize it so that it can be answered 
        without reference to earlier queries, previous answers, or to the item description.
      </promptString>
      <returnStruc>
        {
          "decontextualized_query": "The rewritten query"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="DetectMemoryRequestPrompt">
      <promptString>
        Analyze the following statement from the user. 
        Is the user asking you to remember, that may be relevant to not just this query, but also future queries? 
        If so, what is the user asking us to remember?
        The user should be explicitly asking you to remember something for future queries, 
        not just expressing a requirement for the current query.
        Keep the memory_request field short and do not reference the user or site.
        The user's query is: {request.rawQuery}.
      </promptString>
      <returnStruc>
        {
          "is_memory_request": "True or False",
          "memory_request": "The memory request, if any"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="DetectMultiItemTypeQueryPrompt">
      <promptString>
        Analyze the following query from the user.
        Is the user asking for only one kind of item or are they asking for multiple kinds of items.
        If they are asking for multiple kinds of items, construct indepenent queries for each of the 
        kinds of items, separated by semicolons. The user's query is: {request.query}.
      </promptString>
      <returnStruc>
        {
          "single_item_type_query": "True or False",
          "item_queries": "Separate queries for each of the kinds of items, separated by commas"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="DetectItemTypePrompt">
      <promptString>
        What is the kind of item the query is likely seeking with this query: {request.query}
        
        Common item types include:
        - Recipe (for cooking, food preparation)
        - Movie (for films, cinema)
        - Product (for items to purchase)
        - Restaurant (for dining establishments)
        - Statistics (for demographic data, population statistics, economic indicators about places like counties, cities, states)
        - Item (for general items that don't fit other categories)
        
        If the query is asking about statistical data like population, median income, demographics, or economic indicators for geographic locations, return "Statistics".
      </promptString>
      <returnStruc>
        {
          "item_type": ""
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="DetectQueryTypePrompt">
      <promptString>
        Analyze the following query from the user. 
        Is the user asking for a list of {site.itemType} that match a certain description or are they asking for the
        details of a particular {site.itemType}?
        If the user for the details of a particular {site.itemType}, what is the name of the {site.itemType} and what
        details are they asking for? The user's query is: {request.query}.
      </promptString>
      <returnStruc>
        {
          "item_details_query": "True or False",
          "item_title": "The title of the item type, if any",
          "details_being_asked": "what details the user is asking for"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="RankingPromptWithExplanation">
      <promptString>
        Assign a score between 0 and 100 to the following {site.itemType}
        based on how relevant it is to the user's question. Use your knowledge from other sources, about the item, to make a judgement.
        Provide a short description of the item that is relevant to the user's question, without mentioning the user's question.
        Provide an explanation of the relevance of the item to the user's question, without mentioning the user's question or the score or explicitly mentioning the term relevance.
        If the score is below 75, in the description, include the reason why it is still relevant.
        The user's question is: {request.query}. The item's description is {item.description}
      </promptString>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "description": "short description of the item",
          "explanation": "explanation of the relevance of the item to the user's question"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="RankingPrompt">
      <promptString>
        Assign a score between 0 and 100 to the following item
        based on how relevant it is to the user's question. Use your knowledge from other sources, about the item, to make a judgement. 
        If the score is above 50, provide a short description of the item highlighting the relevance to the user's question, without mentioning the user's question.
        Provide an explanation of the relevance of the item to the user's question, without mentioning the user's question or the score or explicitly mentioning the term relevance.
        If the score is below 75, in the description, include the reason why it is still relevant.
        The user's question is: \"{request.query}\". The item's description in schema.org format is \"{item.description}\".
      </promptString>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "description": "short description of the item"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="RankingPromptForGenerate">
      <promptString>
        Assign a score between 0 and 100 to the following item
        based on useful it might be to answering the user's question. 
        The user's question is: \"{request.query}\".
        The item in schema.org format is: \"{item.description}\". Include a 
        short description of the item in the description field.
      </promptString>
      <returnStruc>
        {
          "score" : "integer between 0 and 100",
          "description" : "short description of the item"
        }
      </returnStruc>
    </Prompt>
     
    
    <Prompt ref="SynthesizePromptForGenerate">
      <promptString>
        Given the following items, synthesize an answer to the user's question. 
        You do not need to include all the items, but you should include the most relevant ones.
        For each of the items included in the answer, provide the URL of the item in 
        the 'urls' field of the structured output. Make sure to include 
        the URL for every one of the items. Do not include the URL in the answer field.
        The user's question is: {request.query}.
        The items are: {request.answers}.
      </promptString>
      <returnStruc>
        {
          "answer" : "string",
          "urls" : "urls of the items included in the answer"
        }
      </returnStruc>
    </Prompt>

     <Prompt ref="SummarizeResultsPrompt">
      <promptString>
        Given the following items, summarize the results as an answer to the user's question. `
        The user's question is: {request.query}. 
        The items are: {request.answers}.
      </promptString>
      <returnStruc>
        {
          "summary" : "string"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="DescriptionPromptForGenerate">
      <promptString>
        The item with the following description is used to answer the user's question. 
        Please provide a description of the item, in the context of the user's question
        and the overall answer.
        The user's question is: {request.query}.
        The overall answer is: {request.answer}.
        The item is: {item.description}.
      </promptString>
      <returnStruc>
        {
          "description" : "string"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="ItemMatchingPrompt">
      <promptString>
        The user is looking for some details about: {request.item_name}
        
        Assign a score between 0 and 100 for whether the following item description
        matches what the user is looking for. A score of 100 means this is exactly
        the item they want, 0 means it's completely unrelated.

        If the score is above 75, also extract the details that the user is looking for.
        Include only the details that the user is explicitly asking for.

        The details requested are: {request.details_requested}. 
        
        Item description: {item.description}
      </promptString>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "item_details": "the specific details requested by the user"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="ExtractItemDetailsPrompt">
      <promptString>
        The user is requesting specific details about an item they previously saw.
        
        The user's query: {request.query}
        The details requested: {request.details_requested}
        
        From the following item description, extract ONLY the specific details the user is asking for.
        Be comprehensive but focused - include all relevant information for what they asked, but don't include unrelated details.
        
        For example:
        - If they ask for "ingredients", include the full ingredients list
        - If they ask for "price", include price and any pricing variations
        - If they ask for "nutrition", include all nutritional information
        - If they ask for "description", provide a clear summary of the item
        - If they ask for "instructions" or "how to make", include step-by-step instructions
        
        Item description: {item.description}
      </promptString>
      <returnStruc>
        {
          "item_name": "the name of the item",
          "requested_details": "the specific details requested by the user, formatted clearly",
          "additional_context": "any important context about these details (optional)"
        }
      </returnStruc>
    </Prompt>


    <Prompt ref="FindItemPrompt"> 
      <promptString>
        The user is looking for for an item named / described as: {item.name}
        Assign a score between 0 and 100 for whether the following item description
        matches the items the user is looking for. A score of 100 means this is exactly
        the item they want, 0 means it's completely unrelated.
        Item description: {item.description}
      </promptString>
      <returnStruc>
        {
          "score": "integer between 0 and 100"
        }
      </returnStruc>
    </Prompt>

    
    
    <Prompt ref="CompareItemsPrompt">
      <promptString>
      The user is looking for a comparison between two items with the following descriptions:
      Item 1: {request.item1_description} 
      vs
      Item 2: {request.item2_description} 
      The user is asking for the following details: {request.details_requested}
      Provide a comparison of the two items, highlighting the differences and similarities.
      The user's query is: {request.query}.
      </promptString>
      <returnStruc>
        {
          "comparison": "Comparison of the two items"
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="CompareItemDetailsPrompt">
      <promptString>
      The user is looking for a comparison between two items with the following descriptions:
      Item 1: {request.item1_description} 
      and
      Item 2: {request.item2_description} 
      The user is asking for the following details: {request.details_requested}
      Provide a comparison of the two items, highlighting the differences and similarities.
      The user's query is: {request.query}.
      </promptString>
      <returnStruc>
        {
          "comparison": "Comparison of the two items"
        }
      </returnStruc>
    </Prompt>

  </Item>

  <Recipe>
    <Prompt ref="DetectMemoryRequestPrompt">
      <promptString>
        Analyze the following statement from the user. 
        Is the user asking you to remember a dietary constraint, that may be relevant
        to not just this query, but also future queries? For example, the user may say
        that they are vegetarian or observe kosher or halal or specify an allergy such
        as gluten or lactose intolerance.
        If so, what is the user asking us to remember?
        The user should be explicitly asking you to remember something for future queries, 
        not just expressing a requirement for the current query.
         Keep the memory_request field short and do not reference the user or site.
        The user's query is: {request.rawQuery}.
      </promptString>
      <returnStruc>
        {
          "is_memory_request": "True or False",
          "memory_request": "The memory request, if any"
        }
      </returnStruc>
    </Prompt>

 <Prompt ref="ItemMatchingPrompt">
      <promptString>
        The user is looking for some details about: {request.item_name} and the users query is: {request.query}.
        Assign a score between 0 and 100 for whether the following item description
        matches what the user is looking for. A score of 100 means this is exactly
        the item they want, 0 means it's completely unrelated.

        If the score is above 75, also extract the details that the user is looking for.
        Include only the details that the user is explicitly asking for.
        If they asked for ingredients, provide only the ingredients list.
        If they asked for instructions, provide only the cooking steps.
        If they asked for nutrition info, provide only nutritional details.
        
        Be direct and specific - extract exactly what they asked for from the data.
        The details requested are: {request.details_requested}. 
        
        Item description: {item.description}
      </promptString>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "item_details": "the specific details requested by the user"
        }
      </returnStruc>
    </Prompt>


  </Recipe>

  <RealEstate>
    <Prompt ref="RequiredInfoPrompt">
      <promptString>
        Answering the user's query requires the location and price range.
        Do you have this information from this
        query or the previous queries or the context or memory about the user? 
        The user's query is: {request.query}. The previous queries are: {request.previousQueries}.
      </promptString>
      <returnStruc>
        {
          "required_info_found": "True or False",
          "user_question": "Question to ask the user for the required information"
        }
      </returnStruc>
    </Prompt>
  </RealEstate>

  <Item>
    <Prompt ref="EnsembleBasePrompt">
      <promptString>
        Based on the user's request: "{request.query}"

        I searched for: {ensemble.queries}

        Here are the search results:
        {ensemble.results}

        Please create a cohesive recommendation that addresses the user's request.
      </promptString>
      <returnStruc>
        {
          "theme": "Brief description of the overall recommendation theme",
          "items": [
            {
              "category": "Category name (e.g., Appetizer, Museum, Footwear)",
              "name": "Specific item name", 
              "description": "Detailed description",
              "why_recommended": "Why this item fits the request",
              "details": {}
            }
          ],
          "overall_tips": ["Any general tips or considerations"]
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="EnsembleMealPlanningPrompt">
      <promptString>
        Based on the user's request: "{request.query}"

        I searched for: {ensemble.queries}

        Here are the search results:
        {ensemble.results}

        Create a complete meal recommendation with:
        1. Appetizer/Starter
        2. Main Course
        3. Dessert

        For each course, provide:
        - Name of the dish
        - Brief description
        - Why it complements the other courses
        - Any relevant details (prep time, difficulty, dietary info)
        - URL of the item

        Include the URL of the item in the 'url' field of the structured output.

        Ensure the courses work well together in terms of flavors, textures, and overall dining experience.
      </promptString>
      <returnStruc>
        {
          "theme": "Brief description of the meal theme",
          "items": [
            {
              "category": "Course type (Appetizer, Main Course, or Dessert)",
              "name": "Specific dish name",
              "description": "Detailed description of the dish",
              "why_recommended": "Why this dish fits the meal",
              "url": "URL of the item",
              "details": {
                "prep_time": "Preparation time",
                "difficulty": "Easy/Medium/Hard",
                "dietary_info": "Any dietary considerations"
              }
            }
          ],
          "overall_tips": ["Tips for preparing or serving the meal"]
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="EnsembleTravelItineraryPrompt">
      <promptString>
        Based on the user's request: "{request.query}"

        I searched for: {ensemble.queries}

        Here are the search results:
        {ensemble.results}

        Create a travel itinerary recommendation with:
        1. Attractions/Museums to visit
        2. Nearby restaurants for meals
        3. Suggested order/timing

        For each recommendation, provide:
        - Name and location
        - Why it's worth visiting
        - Time needed
        - How it connects to other recommendations
        - Any practical tips (hours, tickets, reservations)
        - URL of the item

        Include the URL of the item in the 'url' field of the structured output.
      </promptString>
      <returnStruc>
        {
          "theme": "Brief description of the itinerary theme",
          "items": [
            {
              "category": "Category (Attraction, Museum, Restaurant, etc.)",
              "name": "Specific venue name",
              "description": "Detailed description",
              "why_recommended": "Why this fits the itinerary",
              "url": "URL of the item",
              "details": {
                "location": "Address or area",
                "time_needed": "Estimated duration",
                "best_time": "Recommended time to visit",
                "tickets": "Ticket information if applicable",
                "reservations": "Reservation requirements"
              }
            }
          ],
          "overall_tips": ["General tips for the itinerary"]
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="EnsembleOutfitPrompt">
      <promptString>
        Based on the user's request: "{request.query}"

        I searched for: {ensemble.queries}

        Here are the search results, each with a URL:
        {ensemble.results}

        Create a complete outfit recommendation with:
        1. Essential items (footwear, jacket, base layers)
        2. Accessories
        3. Additional considerations

        For each item, provide:
        - Specific type recommended
        - Why it's suitable for the conditions
        - Key features to look for
        - URL of the item
        - Any alternatives

        Include the URL of the item in the 'url' field of the structured output.

        Consider weather, activity level, and safety.
      </promptString>
      <returnStruc>
        {
          "theme": "Brief description of the outfit purpose",
          "items": [
            {
              "category": "Category (Footwear, Jacket, Accessories, etc.)",
              "name": "Specific item type",
              "description": "Detailed description",
              "why_recommended": "Why this item is suitable",
              "url": "URL of the item",
              "details": {
                "key_features": "Important features to look for",
                "alternatives": "Alternative options",
                "weather_suitability": "How it handles weather conditions",
                "activity_level": "Suitable activity level"
              }
            }
          ],
          "overall_tips": ["General outfit tips and safety considerations"]
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="EnsembleGenericPrompt">
      <promptString>
        Based on the user's request: "{request.query}"

        I searched for: {ensemble.queries}

        Here are the search results:
        {ensemble.results}

        Create a cohesive set of recommendations that work well together.
        For each item, explain why it's recommended and how it complements the other items.
        Include the URL of the item in the 'url' field of the structured output.
      </promptString>
      <returnStruc>
        {
          "theme": "Brief description of the recommendation theme",
          "items": [
            {
              "category": "Item category",
              "name": "Specific item name",
              "description": "Detailed description",
              "why_recommended": "Why this item fits the request",
              "url": "URL of the item",
              "details": {}
            }
          ],
          "overall_tips": ["General tips or considerations"]
        }
      </returnStruc>
    </Prompt>

    <Prompt ref="EnsembleItemRankingPrompt">
      <promptString>
        Given the user's query: "{request.query}"

        And this item:
        Name: {item.name}
        Type: {item.type}
        Description: {item.description}

        Rate how relevant this item is for answering the user's query on a scale of 0-100.
        Consider:
        - Does this item directly address what the user is looking for?
        - Is it the right type of item (e.g., restaurant vs attraction)?
        - Does it match any specific criteria mentioned in the query?

        Provide your response as a JSON object with a 'score' field containing the relevance score.
      </promptString>
      <returnStruc>
        {
          "score": "integer between 0 and 100"
        }
      </returnStruc>
    </Prompt>
  </Item>

  <Statistics>
    <Prompt ref="DetectItemTypePrompt">
      <promptString>
        What is the kind of item the query is likely seeking with this query: {request.query}
        
        Common item types include:
        - Recipe (for cooking, food preparation)
        - Movie (for films, cinema)
        - Product (for items to purchase)
        - Restaurant (for dining establishments)
        - Statistics (for demographic data, population statistics, economic indicators about places like counties, cities, states)
        - Item (for general items that don't fit other categories)
        
        If the query is asking about statistical data like population, median income, demographics, or economic indicators for geographic locations, return "Statistics".
      </promptString>
      <returnStruc>
        {
          "item_type": ""
        }
      </returnStruc>
    </Prompt>
    
    <Prompt ref="RankingPrompt">
      <promptString>
        Assign a score between 0 and 100 to the following statistical data point
        based on how relevant it is to the user's question about demographic or economic indicators.
        The user's question is: "{request.query}". 
        The data point is: "{item.description}".
      </promptString>
      <returnStruc>
        {
          "score": "integer between 0 and 100",
          "description": "short description of the data point"
        }
      </returnStruc>
    </Prompt>
  </Statistics>

</root>
