port: 8000
static_directory: ../static

# the system can be run in development, production, or testing mode.
# if development, various config params can be overridden with query params
# obviously, this cannot be allowed in production.
# in testing mode, exceptions are raised instead of being caught for better error visibility
mode: development # or production or testing. 


# Additional optional configurations
server:
  host: 0.0.0.0
  enable_cors: true
  max_connections: 100
  timeout: 30  # seconds
  
  # SSL configuration (optional)
  ssl:
    enabled: false
    cert_file_env: SSL_CERT_FILE
    key_file_env: SSL_KEY_FILE
    
  # Logging configuration
  logging:
    level: info
    file: ./logs/webserver.log
    
  # Static file serving
  static:
    enable_cache: true
    cache_max_age: 3600  # seconds
    gzip_enabled: true
