# Pangolin Platform Environment Variables
# Copy this file to .env and fill in the values

# Required Pangolin Configuration
DOMAIN=example.com
EMAIL=<EMAIL>

# Optional Pangolin Configuration
ADMIN_SUBDOMAIN=pangolin
ADMIN_USERNAME=<EMAIL>
ADMIN_PASSWORD=changeme
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_HOST=pangolin-postgres

# Optional Component Subdomains
TRAEFIK_SUBDOMAIN=traefik
MIDDLEWARE_MANAGER_SUBDOMAIN=middleware
KOMODO_SUBDOMAIN=komodo
NLWEB_SUBDOMAIN=nlweb
LOGS_SUBDOMAIN=logs
STATIC_PAGE_SUBDOMAIN=www

# Optional Component Configuration
# CrowdSec Security
CROWDSEC_ENROLLMENT_KEY=

# OAuth Authentication
CLIENT_ID=
CLIENT_SECRET=
OAUTH_DOMAIN=oauth.example.com

# Komodo Server Management (requires KOMODO_HOST_IP to enable)
KOMODO_HOST_IP=
KOMODO_PASSKEY=
KOMODO_TITLE=
KOMODO_FIRST_SERVER=
COMPOSE_KOMODO_IMAGE_TAG=latest

# AI/NLWeb Configuration (requires at least one API key to enable)
OPENAI_API_KEY=
AZURE_OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GEMINI_API_KEY=

# Traefik Log Dashboard (requires MAXMIND_LICENSE_KEY to enable)
MAXMIND_LICENSE_KEY=

# System Configuration
TZ=Etc/UTC
